import { useFindAccounts } from "@/modules/financial/hooks/accounts/find-all.hook";
import { getBankIcon } from "@/modules/financial/utils/get-bank-icon";
import { PersonsSelect } from "@/modules/person/components/person-select";
import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Calendar, CreditCard, Receipt, RefreshCw, Search } from "lucide-react";
import { Controller, FormProvider, UseFormReturn } from "react-hook-form";
import { IoAdd } from "react-icons/io5";

interface FilterFormValues {
	description?: string;
	type?: string;
	dueDate?: Date | null;
	paymentDate?: Date | null;
	personId?: string;
	accountId?: string;
}

interface FilterFormProps {
	readonly methods: UseFormReturn<FilterFormValues>;
	readonly onNewBill?: () => void;
}

export function FilterForm({ methods, onNewBill }: FilterFormProps) {
	const { accounts, isLoading } = useFindAccounts();

	return (
		<FormProvider {...methods}>
			<form className="space-y-3">
				{/* Primeira linha - Campos principais */}
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3">
					{/* Descrição */}
					<div className="xl:col-span-2">
						<label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">
							Descrição
						</label>
						<div className="relative">
							<Input
								id="description"
								type="text"
								placeholder="Buscar por descrição..."
								className="w-full h-[45px] rounded-[10px] pl-10"
								{...methods.register("description")}
							/>
							<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>

					{/* Tipo */}
					<div>
						<label htmlFor="type" className="block text-sm font-medium text-gray-600 mb-1">
							Tipo
						</label>
						<div className="relative">
							<Select {...methods.register("type")}>
								<SelectTrigger id="type" className="w-full h-[45px] rounded-[10px] pl-10">
									<SelectValue placeholder="Todos os tipos" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="1">A pagar</SelectItem>
									<SelectItem value="2">A receber</SelectItem>
								</SelectContent>
							</Select>
							<Receipt className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>

					{/* Data de Vencimento */}
					<div>
						<Controller
							control={methods.control}
							name="dueDate"
							render={({ field }) => (
								<div className="relative">
									<DatePickerInput
										className="w-full"
										field={field}
										inputDateClassName="w-full h-[45px] pl-10 rounded-[10px] border-gray-300"
										placeholder="Data de vencimento"
										label="Data de Vencimento"
									/>
									<Calendar className="absolute left-3 top-[47px] -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								</div>
							)}
						/>
					</div>

					{/* Data de Pagamento */}
					<div>
						<Controller
							control={methods.control}
							name="paymentDate"
							render={({ field }) => (
								<div className="relative">
									<DatePickerInput
										className="w-full"
										field={field}
										inputDateClassName="w-full h-[45px] pl-10 rounded-[10px] border-gray-300"
										placeholder="Data de pagamento"
										label="Data de Pagamento"
									/>
									<Calendar className="absolute left-3 top-[47px] -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								</div>
							)}
						/>
					</div>
				</div>

				{/* Segunda linha - Pessoa, Conta e Botão Reset */}
				<div className="flex flex-col sm:flex-row gap-3 items-end">
					{/* Pessoa */}
					<div className="flex-1 min-w-0">
						<label htmlFor="personId" className="block text-sm font-medium text-gray-600 mb-1">
							Pessoa
						</label>
						<div className="relative">
							<Controller
								control={methods.control}
								name="personId"
								render={({ field }) => <PersonsSelect value={field.value} onChange={field.onChange} />}
							/>
						</div>
					</div>

					{/* Conta */}
					<div className="flex-1 min-w-0">
						<label htmlFor="accountId" className="block text-sm font-medium text-gray-600 mb-1">
							Conta
						</label>
						<div className="relative">
							<Controller
								control={methods.control}
								name="accountId"
								render={({ field }) => (
									<Select value={field.value ?? undefined} onValueChange={field.onChange} disabled={isLoading || !accounts}>
										<SelectTrigger id="accountId" className="w-full h-[45px] rounded-[10px] pl-10">
											<SelectValue placeholder={isLoading ? "Carregando contas..." : "Todas as contas"} />
										</SelectTrigger>
										<SelectContent>
											{accounts?.map(account => (
												<SelectItem key={account.id} value={String(account.id)}>
													<span className="flex items-center gap-2">
														{getBankIcon(account.name)}
														{account.name}
													</span>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								)}
							/>
							<CreditCard className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>

					{/* Botão Reset - sempre no final */}
					<div className="w-full sm:w-auto">
						<Button
							type="button"
							variant="outline"
							className="w-full sm:w-auto h-[45px] rounded-[10px] flex items-center gap-2 whitespace-nowrap"
							onClick={() => methods.reset()}
						>
							<RefreshCw size={18} className="text-mainColor" />
							<span>Resetar Filtros</span>
						</Button>
					</div>
				</div>
			</form>

			<div className="md:hidden mt-3 pt-3 border-t border-gray-100">
				<Button
					className="w-full flex items-center justify-center gap-2 bg-mainColor text-white h-[45px] rounded-[10px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
					onClick={onNewBill}
				>
					<IoAdd size={18} />
					Nova Conta
				</Button>
			</div>
		</FormProvider>
	);
}
